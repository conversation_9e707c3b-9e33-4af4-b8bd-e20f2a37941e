/**
 * Digital Core Dashboard Screen
 */

import { DCDashboard } from '@/src/components';
import { tokens, useAppTheme } from '@/src/design-system';
import { StatusBar, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function DCDashboardScreen() {
  const { theme } = useAppTheme();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />
      <DCDashboard />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
  },
});
