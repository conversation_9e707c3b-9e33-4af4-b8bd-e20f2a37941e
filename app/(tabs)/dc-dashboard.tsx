/**
 * Digital Core Dashboard Screen
 */

import { DCDashboard } from '@/src/components';
import { TabLayout } from "@/src/components/layout/TabLayoutExport";
import { useAppTheme } from '@/src/design-system';
import { StatusBar } from 'react-native';

export default function DCDashboardScreen() {
  const { theme } = useAppTheme();

  return (
    <TabLayout scrollable={false} contentPadding={false}>
      <StatusBar
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />
      <DCDashboard />
    </TabLayout>
  );
}
