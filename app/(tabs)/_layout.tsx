import { Tabs } from "expo-router";
import React from "react";
import { Text as RNText, StyleSheet, View } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { tokens, useAppTheme } from "@/src/design-system";

import CompareBarsIcon from "@/assets/icons/eneco/CompareBarsIcon.svg";
import DashboardIcon from "@/assets/icons/eneco/DashboardIcon.svg";
import DocumentIcon from "@/assets/icons/eneco/DocumentIcon.svg";
import LeafIcon from "@/assets/icons/eneco/LeafIcon.svg";
import ProfileIcon from "@/assets/icons/eneco/ProfileIcon.svg";
import { Ionicons } from "@expo/vector-icons";

// Custom tab bar label component
interface TabBarLabelProps {
  focused: boolean;
  color: string;
  children: React.ReactNode;
}

function TabBarLabel({ focused, color, children }: TabBarLabelProps) {
  return (
    <RNText
      style={{
        color,
        fontSize: 12,
        fontFamily: tokens.typography.fontFamilies.base,
        fontWeight: focused ? "500" : "300", // Medium weight for selected, light for unselected
        letterSpacing: tokens.typography.letterSpacings.normal,
        marginTop: 4,
        textAlign: "center",
      }}
    >
      {children}
    </RNText>
  );
}

// Define styles
const styles = StyleSheet.create({
  indicator: {
    position: "absolute",
    top: -15,
    width: 30,
    height: 15,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    backgroundColor: tokens.colors.secondaryColors.accentGreen700,
    alignSelf: "center",
    left: "auto",
    right: "auto",
  },
  tabIconContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 10,
  },
});

export default function TabLayout() {
  const { isDarkMode } = useAppTheme();

  // Use design tokens for colors
  const activeTintColor = isDarkMode
    ? tokens.colors.secondaryColors.accentGreen600
    : tokens.colors.secondaryColors.accentGreen700;

  const inactiveTintColor = isDarkMode
    ? tokens.colors.neutralColors.neutral400
    : tokens.colors.neutralColors.neutral800;

  const backgroundColor = isDarkMode
    ? tokens.colors.backgroundColors.backgroundDark
    : tokens.colors.backgroundColors.backgroundPrimary;

  // Check if we're in development mode
  const isDevMode = __DEV__;

  console.log("isDevMode", isDevMode);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: activeTintColor,
        tabBarInactiveTintColor: inactiveTintColor,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          backgroundColor: backgroundColor,
          borderTopWidth: 0,
          height: 80,
          paddingTop: 10,
          paddingBottom: 10,
          // Remove position absolute to prevent overlap
        },
        tabBarLabel: ({ focused, color, children }) => (
          <TabBarLabel focused={focused} color={color}>
            {children}
          </TabBarLabel>
        ),
        tabBarLabelPosition: "below-icon",
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Dashboard",
          tabBarIcon: ({ color, focused }) => (
            <View style={styles.tabIconContainer}>
              {focused && (
                <View
                  style={[
                    styles.indicator,
                    {
                      backgroundColor: backgroundColor,
                    },
                  ]}
                />
              )}
              <DashboardIcon width={28} height={28} fill={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="energy"
        options={{
          title: "Energy use",
          tabBarIcon: ({ color, focused }) => (
            <View style={styles.tabIconContainer}>
              {focused && (
                <View
                  style={[
                    styles.indicator,
                    {
                      backgroundColor: backgroundColor,
                    },
                  ]}
                />
              )}
              <CompareBarsIcon width={28} height={28} fill={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="sustainable"
        options={{
          title: "Sustainable",
          tabBarIcon: ({ color, focused }) => (
            <View style={styles.tabIconContainer}>
              {focused && (
                <View
                  style={[
                    styles.indicator,
                    {
                      backgroundColor: backgroundColor,
                    },
                  ]}
                />
              )}
              <LeafIcon width={28} height={28} fill={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="service"
        options={{
          title: "Service",
          tabBarIcon: ({ color, focused }) => (
            <View style={styles.tabIconContainer}>
              {focused && (
                <View
                  style={[
                    styles.indicator,
                    {
                      backgroundColor: backgroundColor,
                    },
                  ]}
                />
              )}
              <View>
                <ProfileIcon width={28} height={28} fill={color} />
                <View style={{
                  position: 'absolute',
                  top: -5,
                  right: -10,
                  backgroundColor: tokens.colors.secondaryColors.accentGreen700,
                  borderRadius: 10,
                  width: 20,
                  height: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                  <RNText style={{
                    color: 'white',
                    fontSize: 12,
                    fontWeight: 'bold',
                  }}>
                    3
                  </RNText>
                </View>
              </View>
            </View>
          ),
        }}
      />


      {/* DC Dashboard tab - only visible in development mode */}
      {isDevMode && (
        <Tabs.Screen
          name="dc-dashboard"
          options={{
            title: "DC API",
            tabBarIcon: ({ color, focused }) => (
              <View style={styles.tabIconContainer}>
                {focused && (
                  <View
                    style={[
                      styles.indicator,
                      {
                        backgroundColor: backgroundColor,
                      },
                    ]}
                  />
                )}
                <DocumentIcon width={28} height={28} fill={color} />
              </View>
            ),
          }}
        />
      )}

      {/* Bluetooth Audio Demo tab - only visible in development mode */}
      {isDevMode && (
        <Tabs.Screen
          name="bluetooth"
          options={{
            title: "Bluetooth",
            tabBarIcon: ({ color, focused }) => (
              <View style={styles.tabIconContainer}>
                {focused && (
                  <View
                    style={[
                      styles.indicator,
                      {
                        backgroundColor: backgroundColor,
                      },
                    ]}
                  />
                )}
                <Ionicons name="bluetooth" size={28} color={color} />
              </View>
            ),
          }}
        />
      )}

      {/* Design System tab - only visible in development mode */}
      {isDevMode && (
        <Tabs.Screen
          name="design-system"
          options={{
            title: "Design System",
            tabBarIcon: ({ color, focused }) => (
              <View style={styles.tabIconContainer}>
                {focused && (
                  <View
                    style={[
                      styles.indicator,
                      {
                        backgroundColor: backgroundColor,
                      },
                    ]}
                  />
                )}
                <DocumentIcon width={28} height={28} fill={color} />
              </View>
            ),
          }}
        />
      )}
    </Tabs>
  );
}
