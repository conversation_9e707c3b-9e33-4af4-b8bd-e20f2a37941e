import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Grid,
  Heading,
  Image,
  Paragraph,
  Skeleton,
  Stack,
  Text, tokens
} from "@/src/design-system";
import { Platform, ScrollView, StyleSheet, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Styles
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Will be overridden by theme
    ...(Platform.OS === "web" && {
      height: "100%",
      overflow: "hidden",
    }),
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: tokens.spacing.space[4],
    ...(Platform.OS === "web" && {
      height: "100%",
      overflow: "visible",
    }),
  },
  container: {
    padding: tokens.spacing.space[4],
  },
  section: {
    marginVertical: tokens.spacing.space[4],
  },
  buttonRow: {
    marginVertical: tokens.spacing.space[2],
  },
  badgeRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: tokens.spacing.space[2],
    marginVertical: tokens.spacing.space[2],
  },
  card: {
    marginVertical: tokens.spacing.space[2],
  },
  row: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: tokens.spacing.space[4],
    marginVertical: tokens.spacing.space[2],
  },
  box: {
    width: 100,
    height: 100,
    justifyContent: "center",
    alignItems: "center",
  },
  gridItem: {
    padding: tokens.spacing.space[2],
    backgroundColor: tokens.colors.backgroundColors.backgroundTertiary,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 50,
  },
  imageContainer: {
    width: 200,
    height: 150,
    marginVertical: tokens.spacing.space[2],
  },
});

export default function HomeScreen() {
  return (
    <SafeAreaView style={[styles.safeArea]}>
      <ScrollView
        style={[styles.scrollView]}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Buttons Section */}
        <View style={styles.container}>
           {/* Typography Section */}
           <Heading as="h1">Design System Components</Heading>
          <Paragraph>
            This is a demonstration of the design system components.
          </Paragraph>
          <View style={styles.section}>
            <Heading as="h2">Buttons</Heading>
            <View style={styles.buttonRow}>
              <Button action="primary">Primary Button</Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="secondary">Secondary Button</Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="primaryRebrand">Primary Rebrand</Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="primary" tone="onColor">
                Primary on Color
              </Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="primary" size="compact">
                Compact Button
              </Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="primary" isLoading loadingText="Loading button">
                Loading
              </Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="primary" disabled>
                Disabled
              </Button>
            </View>
            <View style={styles.buttonRow}>
              <Button action="primary" icon="check">
                With Icon
              </Button>
            </View>
          </View>

         
          {/* Heading Component */}
          <View style={styles.section}>
            <Stack gap="4" direction="column">
              <Heading as="h2">Heading Component</Heading>
              <Heading as="h1" size="3XL">
                Heading 3XL (h1)
              </Heading>
              <Heading as="h2" size="2XL">
                Heading 2XL (h2)
              </Heading>
              <Heading as="h3" size="XL">
                Heading XL (h3)
              </Heading>
              <Heading as="h4" size="L">
                Heading L (h4)
              </Heading>
              <Heading as="h5" size="M">
                Heading M (h5)
              </Heading>
              <Heading as="h6" size="S">
                Heading S (h6)
              </Heading>
            </Stack>
          </View>

          {/* Badge Component */}
          <View style={styles.section}>
            <Heading as="h2">Badge Component</Heading>
            <View style={styles.badgeRow}>
              <Badge color="alpha">Alpha</Badge>
              <Badge color="success">Success</Badge>
              <Badge color="warning">Warning</Badge>
              <Badge color="error">Error</Badge>
              <Badge color="highemphasis">High Emphasis</Badge>
            </View>
            <View style={styles.badgeRow}>
              <Badge color="alpha" size="S">
                Small
              </Badge>
              <Badge color="success" size="M">
                Medium
              </Badge>
            </View>
          </View>

          {/* Box Component */}
          <View style={styles.section}>
            <Heading as="h2">Box Component</Heading>
            <View style={styles.row}>
              <Box
                backgroundColor="backgroundPrimary"
                borderRadius="m"
                padding="4"
                style={styles.box}
              >
                <Text>Primary</Text>
              </Box>
              <Box
                backgroundColor="backgroundSecondary"
                borderRadius="l"
                padding="4"
                style={styles.box}
              >
                <Text>Secondary</Text>
              </Box>
              <Box
                backgroundColor="backgroundTertiary"
                borderRadius="s"
                padding="4"
                style={styles.box}
              >
                <Text>Tertiary</Text>
              </Box>
            </View>
          </View>

          {/* Grid Component */}
          <View style={styles.section}>
            <Heading as="h2">Grid Component</Heading>
            <Grid columns="3" gap="2">
              <Grid.Item style={styles.gridItem}>
                <Text>Item 1</Text>
              </Grid.Item>
              <Grid.Item style={styles.gridItem}>
                <Text>Item 2</Text>
              </Grid.Item>
              <Grid.Item style={styles.gridItem}>
                <Text>Item 3</Text>
              </Grid.Item>
              <Grid.Item style={styles.gridItem}>
                <Text>Item 4</Text>
              </Grid.Item>
              <Grid.Item style={styles.gridItem}>
                <Text>Item 5</Text>
              </Grid.Item>
              <Grid.Item style={styles.gridItem}>
                <Text>Item 6</Text>
              </Grid.Item>
            </Grid>
          </View>

          {/* Stack Component */}
          <View style={styles.section}>
            <Heading as="h2">Stack Component</Heading>
            <Stack gap="4" direction="column">
              <Stack.Item>
                <Box backgroundColor="backgroundSecondary" padding="2">
                  <Text>Stack Item 1</Text>
                </Box>
              </Stack.Item>
              <Stack.Item>
                <Box backgroundColor="backgroundSecondary" padding="2">
                  <Text>Stack Item 2</Text>
                </Box>
              </Stack.Item>
              <Stack.Item>
                <Box backgroundColor="backgroundSecondary" padding="2">
                  <Text>Stack Item 3</Text>
                </Box>
              </Stack.Item>
            </Stack>
          </View>

          {/* Image Component */}
          <View style={styles.section}>
            <Heading as="h2">Image Component</Heading>
            <Image
              src="https://picsum.photos/200/150"
              alt="Sample image"
              width={200}
              height={150}
              borderRadius={8}
              objectFit="cover"
            />
          </View>

          {/* Skeleton Component */}
          <View style={styles.section}>
            <Heading as="h2">Skeleton Component</Heading>
            <View style={styles.row}>
              <Skeleton variant="rectangular" width={100} height={100} />
              <Skeleton variant="circle" width={100} height={100} />
              <Skeleton variant="text" width={200} height={20} />
            </View>
          </View>

          {/* Cards Section */}
          <View style={styles.section}>
            <Heading as="h2">Cards Component</Heading>
            <Card style={styles.card} corners="rounded" elevation="S">
              <Card.Content>
                <Heading as="h4" size="M">
                  Card with Content
                </Heading>
                <Paragraph>
                  This is a basic card with content. Cards can contain various
                  elements like text, images, and actions.
                </Paragraph>
              </Card.Content>
            </Card>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
