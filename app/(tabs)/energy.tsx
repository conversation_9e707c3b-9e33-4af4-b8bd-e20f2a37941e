import { tokens, useAppTheme } from "@/src/design-system";
import { StatusBar, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { EnergyDashboard } from "../energy-usage";

export default function EnergyScreen() {
  const { theme } = useAppTheme();

  return (
    <SafeAreaView style={[styles.container]}>
      <StatusBar
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />
      <EnergyDashboard initialPeriod="month" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: tokens.spacing.space[4],
  },
  box: {
    marginTop: tokens.spacing.space[4],
  },
});
