import { useAuth } from '@/src/auth/AuthContext';
import Icon from '@/src/components/Icon';
import { TabLayout } from "@/src/components/layout/TabLayoutExport";
import AddressDropdown from '@/src/components/service/AddressDropdown';
import ListItem from '@/src/components/service/ListItem';
import SectionHeader from '@/src/components/service/SectionHeader';
import {
  Button, Text, tokens, useAppTheme
} from "@/src/design-system";
import React from 'react';
import { Alert, Pressable, StatusBar, StyleSheet, View } from 'react-native';



// Sample data
const customerData = {
  name: "F Abcdefghi",
  customerNumber: "14911126",
  address: "Duivelandsestraat 29",
};

const paymentItems = [
  {
    id: 'monthly-payment',
    icon: 'calendar-today',
    title: 'Monthly payment amount',
    subtitle: '€ 223',
  },
  {
    id: 'payment-method',
    icon: 'euro',
    title: 'Payment method',
    subtitle: 'Manual transfer',
  },
  {
    id: 'bills',
    icon: 'description',
    title: 'Bills',
    subtitle: '3 overdue bills',
    badge: 3,
  },
];

const dataItems = [
  {
    id: 'personal-data',
    icon: 'person',
    title: 'Personal data',
    subtitle: '06012345672 - <EMAIL>',
  },
  {
    id: 'contact-preferences',
    icon: 'email',
    title: 'Contact preferences',
    subtitle: 'How may we contact you',
  },
  {
    id: 'address-change',
    icon: 'local-shipping',
    title: 'Register change of address',
    subtitle: 'Current: 2583KK, \'s-gravenhage',
  },
];

export default function ServiceScreen() {
  const { theme } = useAppTheme();
  const { logout, isLoading } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      Alert.alert('Logout Failed', 'An error occurred while logging out. Please try again.');
    }
  };

  return (
    <TabLayout>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.backgroundPrimary} />
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View>
              <Text variant="titleLarge" style={{ color: theme.colors.textPrimary }}>
                {customerData.name}
              </Text>
              <Text variant="bodyMedium" style={{ color: 'rgba(47, 45, 45, 0.7)' }}>
                Customer number {customerData.customerNumber}
              </Text>
            </View>

            <Pressable
              style={({ pressed }) => [
                styles.settingsButton,
                { opacity: pressed ? 0.7 : 1 }
              ]}
              onPress={() => {}}
            >
              <Icon name="settings" size={24} color={theme.colors.textPrimary} />
            </Pressable>
          </View>

          <View style={styles.addressContainer}>
            <AddressDropdown address={customerData.address} />
          </View>
        </View>

        {/* Customer Service Section */}
        <ListItem
          leftIcon="chat"
          title="Customer service"
          onPress={() => {}}
        />

        {/* Payments Section */}
        <SectionHeader title="Payments" />

        {paymentItems.map((item, index) => (
          <ListItem
            key={item.id}
            leftIcon={item.icon}
            title={item.title}
            subtitle={item.subtitle}
            badge={item.badge}
            isLast={index === paymentItems.length - 1}
            onPress={() => {}}
          />
        ))}

        {/* Data Section */}
        <SectionHeader title="Data" />

        {dataItems.map((item, index) => (
          <ListItem
            key={item.id}
            leftIcon={item.icon}
            title={item.title}
            subtitle={item.subtitle}
            isLast={index === dataItems.length - 1}
            onPress={() => {}}
          />
        ))}

        {/* Contract Renewal Banner */}
        <Pressable
          style={({ pressed }) => [
            styles.renewalBanner,
            {
              backgroundColor: theme.colors.primary,
              opacity: pressed ? 0.9 : 1,
            },
          ]}
          onPress={() => {}}
        >
          <Icon name="autorenew" size={24} color={theme.colors.onPrimary} />
          <Text
            variant="bodyLarge"
            style={[styles.renewalText, { color: theme.colors.onPrimary }]}
          >
            You can now renew your contract!
          </Text>
        </Pressable>

        {/* Logout Button */}
        <View style={styles.logoutButtonContainer}>
          <Button
            action="secondary"
            onPress={handleLogout}
            disabled={isLoading}
            isLoading={isLoading}
          >
            Sign Out
          </Button>
        </View>
    </TabLayout>
  );
}

const styles = StyleSheet.create({
  header: {
    padding: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  settingsButton: {
    padding: 8,
    marginTop: -8,
    marginRight: -8,
  },
  addressContainer: {
    marginTop: 8,
  },
  renewalBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 24,
    borderRadius: 8,
  },
  renewalText: {
    marginLeft: 12,
    fontWeight: '500',
  },
  logoutButtonContainer: {
    marginTop: tokens.spacing.space[6],
    alignItems: 'center',
    paddingHorizontal: 16,
  },
});
