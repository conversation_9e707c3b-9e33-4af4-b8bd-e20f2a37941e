import { TabLayout } from "@/src/components/layout/TabLayoutExport";
import { tokens, useAppTheme } from "@/src/design-system";
import { Box, Text } from "@/src/design-system/components";
import React, { useState } from 'react';
import { Image, ImageSourcePropType, Pressable, StatusBar, StyleSheet, View } from 'react-native';

// Define types for our components
type Product = {
  id: string;
  image: ImageSourcePropType;
  title: string;
  description: string;
  category: string;
}

type Category = {
  id: string;
  title: string;
}

type ProductCardProps = {
  image: ImageSourcePropType;
  title: string;
  description: string;
  onPress: () => void;
}

type CategoryButtonProps = {
  title: string;
  isSelected: boolean;
  onPress: () => void;
}

// Product data
const products: Product[] = [
  {
    id: 'service-gemak',
    image: require('../../assets/images/stock/service-gemak.jpg'),
    title: 'Eneco ServiceGemak Cv-ketel',
    description: 'A good service subscription for a safe and efficient central heating boiler.',
    category: 'heating'
  },
  {
    id: 'toon',
    image: require('../../assets/images/stock/toon.png'),
    title: 'Toon® from Eneco',
    description: 'Reduce your energy intake with the smart thermostat.',
    category: 'smart'
  },
  {
    id: 'boiler',
    image: require('../../assets/images/stock/boiler.jpg'),
    title: 'Boiler for hire and for sale',
    description: 'Always a boiler that will fit you and your home.',
    category: 'heating'
  },
  {
    id: 'heatpump',
    image: require('../../assets/images/stock/heat-pump.jpg'),
    title: 'Heatpump',
    description: 'Save on your gas consumption',
    category: 'heating'
  }
];

// Category data
const categories: Category[] = [
  { id: 'all', title: 'All products' },
  { id: 'smart', title: 'Smart heating' },
  { id: 'insights', title: 'Insights and saving' }
];

// ProductCard component
const ProductCard: React.FC<ProductCardProps> = ({ image, title, description, onPress }) => {
  const { theme } = useAppTheme();

  return (
    <Pressable
      style={({ pressed }) => [
        styles.productCard,
        pressed && { opacity: 0.9 }
      ]}
      onPress={onPress}
      accessibilityLabel={title}
      accessibilityRole="button"
    >
      <Image
        source={image}
        style={styles.productImage}
        resizeMode="cover"
        accessibilityLabel={`Image of ${title}`}
      />
      <View style={styles.productContent}>
        <Text
          variant="titleMedium"
          style={{ color: theme.colors.textPrimary, marginBottom: tokens.spacing.space[1] }}
        >
          {title}
        </Text>
        <Text
          variant="bodyMedium"
          style={{ color: theme.colors.onSurface }}
        >
          {description}
        </Text>
      </View>
    </Pressable>
  );
};

// CategoryButton component
const CategoryButton: React.FC<CategoryButtonProps> = ({ title, isSelected, onPress }) => {
  const { theme } = useAppTheme();

  return (
    <Pressable
      style={[
        styles.categoryButton,
        isSelected && { borderBottomColor: theme.colors.primary, borderBottomWidth: 2 }
      ]}
      onPress={onPress}
      accessibilityLabel={title}
      accessibilityRole="button"
      accessibilityState={{ selected: isSelected }}
    >
      <Text
        variant="bodyLarge"
        style={[
          styles.categoryButtonText,
          isSelected && { color: theme.colors.primary, fontWeight: "700" }
        ]}
      >
        {title}
      </Text>
    </Pressable>
  );
};

export default function SustainableScreen() {
  const { theme } = useAppTheme();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCategoryMenu, setShowCategoryMenu] = useState(false);

  // Filter products based on selected category
  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(product => product.category === selectedCategory);

  // Handle product press
  const handleProductPress = (product: Product) => {
    console.log('Product pressed:', product.title);
    // Navigation to product details would go here
  };

  // Toggle category menu
  const toggleCategoryMenu = () => {
    setShowCategoryMenu(!showCategoryMenu);
  };

  return (
    <TabLayout>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.backgroundPrimary} />
        {/* Header Dropdown */}
        <Pressable
          style={[
            styles.headerDropdown,
            { backgroundColor: 'rgba(0, 0, 0, 0.05)' }
          ]}
          onPress={toggleCategoryMenu}
          accessibilityLabel="Toggle category menu"
          accessibilityRole="button"
          accessibilityHint="Shows or hides the category selection menu"
        >
          <Text
            variant="titleMedium"
            style={{ color: theme.colors.textPrimary }}
          >
            {categories.find(cat => cat.id === selectedCategory)?.title || 'All products'}
          </Text>
          <View style={{ width: 24, height: 24 }}>
            {/* Using a simple text character for the chevron since we're having issues with SVG imports */}
            <Text
              variant="titleLarge"
              style={{ color: theme.colors.textPrimary, textAlign: 'center' }}
            >
              ▼
            </Text>
          </View>
        </Pressable>

        {/* Product Grid */}
        <View style={styles.productGrid}>
          {filteredProducts.map(product => (
            <ProductCard
              key={product.id}
              image={product.image}
              title={product.title}
              description={product.description}
              onPress={() => handleProductPress(product)}
            />
          ))}
        </View>

        {/* Category Menu - Shown conditionally */}
        {showCategoryMenu && (
          <Box
            backgroundColor="backgroundSecondary"
            style={styles.categoryMenu}
          >
            {categories.map(category => (
              <CategoryButton
                key={category.id}
                title={category.title}
                isSelected={selectedCategory === category.id}
                onPress={() => {
                  setSelectedCategory(category.id);
                  setShowCategoryMenu(false);
                }}
              />
            ))}
          </Box>
        )}
    </TabLayout>
  );
}

const styles = StyleSheet.create({
  headerDropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.space[4],
    padding: tokens.spacing.space[4],
    borderRadius: tokens.borders.radii.m,
  },
  productGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productCard: {
    width: '48%', // Slightly less than half to allow for spacing
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: tokens.borders.radii.m,
    marginBottom: tokens.spacing.space[4],
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 150,
  },
  productContent: {
    padding: tokens.spacing.space[3],
  },
  categoryMenu: {
    borderRadius: tokens.borders.radii.m,
    overflow: 'hidden',
    marginTop: tokens.spacing.space[4],
  },
  categoryButton: {
    paddingVertical: tokens.spacing.space[4],
    paddingHorizontal: tokens.spacing.space[4],
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.borderColors.borderDividerLowEmphasis,
  },
  categoryButtonText: {
    textAlign: 'center',
  },
});
