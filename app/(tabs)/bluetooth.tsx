/**
 * Bluetooth Audio Demo Screen
 */

import { BluetoothAudioDemo } from '@/src/components/BluetoothAudioDemo';
import { tokens } from '@/src/design-system';
import React from 'react';
import { StyleSheet, View } from 'react-native';

/**
 * Bluetooth Audio Demo Screen
 * Fixed: Removed ScrollView to prevent VirtualizedList nesting warning
 */
export default function BluetoothScreen() {
  return (
    <View style={styles.container}>
      <BluetoothAudioDemo />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
    paddingTop: tokens.spacing.space[12], // Safe area padding
  },
});
