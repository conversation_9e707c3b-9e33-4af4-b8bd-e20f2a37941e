/**
 * Bluetooth Audio Demo Screen
 */

import { BluetoothAudioDemo } from '@/src/components/BluetoothAudioDemo';
import { tokens } from '@/src/design-system';
import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

/**
 * Bluetooth Audio Demo Screen
 * Fixed: Removed ScrollView to prevent VirtualizedList nesting warning
 */
export default function BluetoothScreen() {
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <BluetoothAudioDemo />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
  },
});
