/**
 * Common Tab Layout Component
 * Provides consistent layout for all tab screens
 */

import { tokens } from '@/src/design-system';
import React from 'react';
import { ScrollView, StyleSheet, View, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface TabLayoutProps {
  children: React.ReactNode;
  backgroundColor?: string;
  scrollable?: boolean;
  contentPadding?: boolean;
  style?: ViewStyle;
}

export const TabLayout: React.FC<TabLayoutProps> = ({
  children,
  backgroundColor = tokens.colors.backgroundColors.backgroundSecondary,
  scrollable = true,
  contentPadding = true,
  style,
}) => {
  const containerStyle = [
    styles.container,
    { backgroundColor },
    style,
  ];

  const contentStyle = [
    styles.content,
    contentPadding && styles.contentPadding,
  ];

  if (scrollable) {
    return (
      <SafeAreaView style={containerStyle}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[contentStyle, styles.scrollContent]}
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={containerStyle}>
      <View style={contentStyle}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentPadding: {
    padding: tokens.spacing.space[4],
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: tokens.spacing.space[6], // Extra space for tab bar
  },
});
