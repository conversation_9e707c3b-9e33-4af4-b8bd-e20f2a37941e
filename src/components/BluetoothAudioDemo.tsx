/**
 * Bluetooth Audio Demo Component
 *
 * This component demonstrates Bluetooth audio playback functionality.
 */

import React, { useEffect } from 'react';
import { ActivityIndicator, StyleSheet, TouchableOpacity, View } from 'react-native';
import { tokens } from '../design-system';
import { Heading, Paragraph } from '../design-system/components';
import { Button } from '../design-system/components/Button';
import { useBluetoothAudio } from '../hooks/useBluetoothAudio';
import { BluetoothDevice } from '../services/BluetoothService';

/**
 * Bluetooth Audio Demo Component
 */
export const BluetoothAudioDemo: React.FC = () => {
  const {
    isBluetoothEnabled,
    isScanning,
    isConnecting,
    isPlaying,
    devices,
    connectedDevice,
    error,
    startScan,
    stopScan,
    connectToDevice,
    disconnectFromDevice,
    playAudio,
    pauseAudio,
    resetError,
  } = useBluetoothAudio();

  // Initialize component on mount only
  useEffect(() => {
    console.log('🔵 BluetoothAudioDemo: Component mounted');
    console.log('🔵 BluetoothAudioDemo: Bluetooth enabled:', isBluetoothEnabled);
    console.log('🔵 BluetoothAudioDemo: Is scanning:', isScanning);
    console.log('🔵 BluetoothAudioDemo: Devices found:', devices.length);

    // Clean up on unmount
    return () => {
      console.log('🔵 BluetoothAudioDemo: Component unmounting, cleaning up...');
      stopScan();
      if (connectedDevice) {
        disconnectFromDevice(connectedDevice.id);
      }
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // No automatic scanning - let users control when to scan
  // This prevents the infinite loop issue

  // Custom handlers with logging
  const handleStartScan = () => {
    console.log('🔵 BluetoothAudioDemo: User clicked Start Scan');
    console.log('🔵 BluetoothAudioDemo: Current state - isScanning:', isScanning, 'devices:', devices.length);
    startScan();
  };

  const handleStopScan = () => {
    console.log('🔵 BluetoothAudioDemo: User clicked Stop Scan');
    console.log('🔵 BluetoothAudioDemo: Current state - isScanning:', isScanning, 'devices:', devices.length);
    stopScan();
  };

  // Render a device item
  const renderDeviceItem = ({ item }: { item: BluetoothDevice }) => {
    const isConnectedDevice = connectedDevice?.id === item.id;

    return (
      <TouchableOpacity
        style={[
          styles.deviceItem,
          isConnectedDevice && styles.connectedDeviceItem,
        ]}
        onPress={() => {
          if (isConnectedDevice) {
            disconnectFromDevice(item.id);
          } else {
            connectToDevice(item.id);
          }
        }}
        disabled={isConnecting}
      >
        <View style={styles.deviceInfo}>
          <Paragraph style={styles.deviceName}>{item.name || 'Unknown Device'}</Paragraph>
          <Paragraph style={styles.deviceId}>{item.id}</Paragraph>
        </View>
        {isConnectedDevice && (
          <View style={styles.connectedIndicator} />
        )}
      </TouchableOpacity>
    );
  };

  // Render error message
  const renderError = () => {
    if (!error) return null;

    return (
      <View style={styles.errorContainer}>
        <Paragraph style={styles.errorText}>{error.message}</Paragraph>
        <Button onPress={resetError} style={styles.errorButton}>
          Dismiss
        </Button>
      </View>
    );
  };

  // Render the connected device section
  const renderConnectedDevice = () => {
    if (!connectedDevice) return null;

    return (
      <View style={styles.connectedDeviceContainer}>
        <View style={styles.connectedDeviceHeader}>
          <View style={styles.connectedIndicator} />
          <Heading as="h3" size="S" style={styles.connectedDeviceTitle}>
            Connected to {connectedDevice.name}
          </Heading>
        </View>

        <View style={styles.audioControls}>
          <Button
            onPress={isPlaying ? pauseAudio : playAudio}
            style={styles.audioButton}
          >
            {isPlaying ? 'Pause Demo Audio' : 'Play Demo Audio'}
          </Button>
        </View>
      </View>
    );
  };

  // Debug logging
  console.log('🔵 BluetoothAudioDemo render:', {
    isBluetoothEnabled,
    isScanning,
    devicesCount: devices.length,
    hasError: !!error,
    connectedDevice: !!connectedDevice
  });

  return (
    <View style={styles.container}>
      <Heading as="h2" size="M" style={styles.heading}>
        Bluetooth Audio Demo
      </Heading>

      {!isBluetoothEnabled ? (
        <View style={styles.messageContainer}>
          <Paragraph style={styles.message}>
            Bluetooth is not enabled. Please enable Bluetooth to continue.
          </Paragraph>
          <Button onPress={startScan} style={styles.actionButton}>
            Check Again
          </Button>
        </View>
      ) : (
        <View style={styles.content}>
          {renderError()}

          {renderConnectedDevice()}

          <View style={styles.devicesContainer}>
            <View style={styles.devicesHeader}>
              <Heading as="h3" size="S" style={styles.devicesTitle}>
                Available Devices
              </Heading>
              <Button
                onPress={isScanning ? handleStopScan : handleStartScan}
                style={styles.scanButton}
              >
                {isScanning ? 'Stop Scan' : 'Scan'}
              </Button>
            </View>

            {isScanning && devices.length === 0 && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator
                  size="large"
                  color={tokens.colors.secondaryColors.accentGreen700}
                />
                <Paragraph style={styles.loadingText}>
                  Scanning for devices...
                </Paragraph>
              </View>
            )}

            {!isScanning && devices.length === 0 && !error && (
              <View style={styles.messageContainer}>
                <Paragraph style={styles.message}>
                  {isBluetoothEnabled
                    ? "Ready to scan for Bluetooth audio devices. Make sure your devices are in pairing mode, then tap 'Scan' to begin."
                    : "Please enable Bluetooth to scan for audio devices."
                  }
                </Paragraph>
              </View>
            )}

            {devices.length > 0 && (
              <View style={styles.devicesListContainer}>
                {devices.map((device) => (
                  <View key={device.id}>
                    {renderDeviceItem({ item: device })}
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    padding: tokens.spacing.space[4],
    minHeight: 200, // Ensure minimum height for debugging
  },
  content: {
    flex: 1,
  },
  devicesListContainer: {
    flex: 1,
  },
  heading: {
    marginBottom: tokens.spacing.space[4],
  },
  messageContainer: {
    padding: tokens.spacing.space[4],
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
    borderRadius: tokens.borders.radii.s,
    marginBottom: tokens.spacing.space[4],
    alignItems: 'center',
  },
  message: {
    textAlign: 'center',
    marginBottom: tokens.spacing.space[3],
  },
  actionButton: {
    marginTop: tokens.spacing.space[2],
  },
  devicesContainer: {
    marginTop: tokens.spacing.space[4],
  },
  devicesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.space[3],
  },
  devicesTitle: {
    flex: 1,
  },
  scanButton: {
    minWidth: 100,
  },
  devicesList: {
    maxHeight: 300,
  },
  devicesListContent: {
    paddingBottom: tokens.spacing.space[2],
  },
  deviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: tokens.spacing.space[3],
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
    borderRadius: tokens.borders.radii.s,
    marginBottom: tokens.spacing.space[2],
  },
  connectedDeviceItem: {
    borderColor: tokens.colors.secondaryColors.accentGreen700,
    borderWidth: 1,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontWeight: 'bold',
    marginBottom: tokens.spacing.space[1],
  },
  deviceId: {
    fontSize: 12,
    color: tokens.colors.textColors.textLowEmphasis,
  },
  connectedIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: tokens.colors.secondaryColors.accentGreen700,
    marginLeft: tokens.spacing.space[2],
  },
  loadingContainer: {
    padding: tokens.spacing.space[4],
    alignItems: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing.space[2],
  },
  errorContainer: {
    padding: tokens.spacing.space[3],
    backgroundColor: tokens.colors.feedbackColors.feedbackBackgroundError,
    borderRadius: tokens.borders.radii.s,
    marginBottom: tokens.spacing.space[4],
  },
  errorText: {
    color: tokens.colors.feedbackColors.feedbackError,
    marginBottom: tokens.spacing.space[2],
  },
  errorButton: {
    alignSelf: 'flex-end',
  },
  connectedDeviceContainer: {
    padding: tokens.spacing.space[3],
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
    borderRadius: tokens.borders.radii.s,
    marginBottom: tokens.spacing.space[4],
  },
  connectedDeviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: tokens.spacing.space[3],
  },
  connectedDeviceTitle: {
    marginLeft: tokens.spacing.space[2],
  },
  audioControls: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  audioButton: {
    minWidth: 200,
  },
});
